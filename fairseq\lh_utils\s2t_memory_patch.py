#!/usr/bin/env python3
"""
Speech-to-Text 内存优化补丁
解决训练过程中的内存泄漏问题

主要问题：
1. logging_outputs 累积导致内存泄漏
2. metrics aggregators 过多导致内存泄漏
3. 中间变量未及时释放
4. CUDA缓存未定期清理
5. 数据集缓存过大

使用方法：
在训练脚本开始时调用 apply_memory_patches()
在创建trainer后调用 apply_memory_optimizations(trainer), 如果 trainer.train_step 已经被包装过 不会重复包装
"""

import gc
import torch
import logging
from typing import Dict, Any, List, Optional
from functools import wraps

logger = logging.getLogger(__name__)


# 全局内存管理器，避免重复清理
class GlobalMemoryManager:
    """全局内存管理器，统一管理CUDA缓存清理和垃圾回收"""

    def __init__(self):
        self.step_count = 0
        self.peak_memory = 0
        self.cuda_cleanup_interval = 100  # 每100步清理CUDA缓存
        self.gc_cleanup_interval = 200    # 每200步进行垃圾回收

    def step(self, trainer_step_count=None):
        """统一的内存管理步骤"""
        # 使用trainer的步数或内部计数
        if trainer_step_count is not None:
            current_step = trainer_step_count
        else:
            self.step_count += 1
            current_step = self.step_count

        if torch.cuda.is_available():
            current_memory = torch.cuda.memory_allocated()
            self.peak_memory = max(self.peak_memory, current_memory)

            # 定期清理CUDA缓存
            if current_step % self.cuda_cleanup_interval == 0:
                torch.cuda.empty_cache()
                logger.info(f"Step {current_step}: CUDA cache cleared")

        # 定期进行垃圾回收
        if current_step % self.gc_cleanup_interval == 0:
            collected = gc.collect()
            if collected > 0:
                logger.info(f"Step {current_step}: GC collected {collected} objects")

    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存统计信息"""
        stats = {
            "step_count": self.step_count,
            "peak_memory_mb": self.peak_memory / 1024 / 1024 if self.peak_memory > 0 else 0,
        }

        if torch.cuda.is_available():
            stats.update({
                "cuda_allocated_mb": torch.cuda.memory_allocated() / 1024 / 1024,
                "cuda_reserved_mb": torch.cuda.memory_reserved() / 1024 / 1024,
            })

        return stats


# 全局内存管理器实例
global_memory_manager = GlobalMemoryManager()


def memory_efficient_train_step(original_train_step):
    """装饰器：为train_step添加内存优化，使用全局内存管理器避免重复清理"""
    @wraps(original_train_step)
    def wrapper(self, samples, raise_oom=False):
        # 执行原始训练步骤
        result = original_train_step(self, samples, raise_oom)

        # 使用全局内存管理器进行统一的内存管理
        num_updates = self.get_num_updates()
        global_memory_manager.step(trainer_step_count=num_updates)

        return result

    return wrapper


def memory_efficient_logging_outputs(original_method):
    """装饰器：优化logging_outputs处理"""
    @wraps(original_method)
    def wrapper(self, logging_outputs, *args, **kwargs):
        # 限制logging_outputs的大小
        if isinstance(logging_outputs, list) and len(logging_outputs) > 20:
            logging_outputs = logging_outputs[-20:]
        
        result = original_method(self, logging_outputs, *args, **kwargs)
        
        # 清理logging_outputs
        if isinstance(logging_outputs, list):
            logging_outputs.clear()
        
        return result
    
    return wrapper


def patch_trainer_memory_issues():
    """修补Trainer类的内存问题"""
    from fairseq.trainer import Trainer
    
    # 修补train_step方法
    if hasattr(Trainer, 'train_step'):
        original_train_step = Trainer.train_step
        Trainer.train_step = memory_efficient_train_step(original_train_step)
        logger.info("Patched Trainer.train_step for memory efficiency")
    
    # 修补_reduce_and_log_stats方法
    if hasattr(Trainer, '_reduce_and_log_stats'):
        original_method = Trainer._reduce_and_log_stats
        Trainer._reduce_and_log_stats = memory_efficient_logging_outputs(original_method)
        logger.info("Patched Trainer._reduce_and_log_stats for memory efficiency")


def patch_metrics_memory_issues():
    """修补metrics模块的内存问题"""
    from fairseq.logging import metrics
    
    # 添加定期清理功能
    original_log_scalar = metrics.log_scalar
    
    def memory_efficient_log_scalar(key, value, weight=1, priority=10, round=None):
        result = original_log_scalar(key, value, weight, priority, round)
        
        # 定期清理metrics
        if len(metrics._aggregators) > 100:
            metrics.periodic_cleanup(max_aggregators=50)
        
        return result
    
    metrics.log_scalar = memory_efficient_log_scalar
    logger.info("Patched metrics.log_scalar for memory efficiency")


def patch_dataset_memory_issues():
    """修补数据集相关的内存问题"""
    try:
        from fairseq.data.lru_cache_dataset import LRUCacheDataset
        
        # 为LRUCacheDataset添加自动清理
        original_getitem = LRUCacheDataset.__getitem__
        
        def memory_efficient_getitem(self, index):
            result = original_getitem(self, index)
            
            # 每1000次访问清理一次缓存
            if not hasattr(self, '_access_count'):
                self._access_count = 0
            self._access_count += 1
            
            if self._access_count % 1000 == 0:
                if hasattr(self, 'clear_cache'):
                    self.clear_cache()
            
            return result
        
        LRUCacheDataset.__getitem__ = memory_efficient_getitem
        logger.info("Patched LRUCacheDataset for memory efficiency")
        
    except ImportError:
        logger.warning("Could not patch LRUCacheDataset - module not found")


def patch_speech_to_text_model():
    """修补speech_to_text模型的内存问题"""
    try:
        from fairseq.models.speech_to_text.s2t_transformer import S2TTransformerModel
        
        original_forward = S2TTransformerModel.forward
        
        def memory_efficient_forward(self, src_tokens, src_lengths, prev_output_tokens):
            # 执行原始forward
            result = original_forward(self, src_tokens, src_lengths, prev_output_tokens)
            
            # 清理中间变量
            if hasattr(self, '_intermediate_cache'):
                self._intermediate_cache.clear()
            
            return result
        
        S2TTransformerModel.forward = memory_efficient_forward
        logger.info("Patched S2TTransformerModel.forward for memory efficiency")
        
    except ImportError:
        logger.warning("Could not patch S2TTransformerModel - module not found")


def configure_memory_efficient_training():
    """配置内存高效的训练参数"""
    import os

    # 设置PyTorch内存管理
    if torch.cuda.is_available():
        # 启用内存池
        torch.cuda.empty_cache()

        # 设置内存分配策略
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'

    # 设置垃圾回收
    gc.set_threshold(700, 10, 10)  # 更频繁的垃圾回收

    logger.info("Configured memory efficient training parameters")


def optimize_model_memory(model):
    """优化模型内存使用"""
    # 清理模型中可能的缓存
    if hasattr(model, 'clear_cache'):
        model.clear_cache()

    # 对于包含LRU缓存的数据集，清理缓存
    def clear_dataset_cache(module):
        if hasattr(module, 'clear_cache'):
            module.clear_cache()
        for child in module.children():
            clear_dataset_cache(child)

    clear_dataset_cache(model)


def cleanup_logging_outputs(logging_outputs, max_size=10):
    """清理logging_outputs，防止内存累积"""
    if len(logging_outputs) > max_size:
        # 只保留最近的几个输出
        return logging_outputs[-max_size:]
    return logging_outputs


def safe_del(*variables):
    """安全删除变量"""
    for var in variables:
        try:
            if var is not None:
                del var
        except:
            pass


def apply_memory_patches():
    """应用所有内存优化补丁"""
    logger.info("Applying memory optimization patches for speech-to-text training...")

    # 配置内存高效训练
    configure_memory_efficient_training()

    # 应用各种补丁
    patch_trainer_memory_issues()
    patch_metrics_memory_issues()
    patch_dataset_memory_issues()
    patch_speech_to_text_model()

    logger.info("All memory optimization patches applied successfully!")


def apply_memory_optimizations(trainer):
    """为训练器应用内存优化，与apply_memory_patches()配合使用"""
    logger.info("Applying memory optimizations to trainer instance...")

    # 如果trainer已经被patch过，不需要重复包装
    if hasattr(trainer, '_memory_optimized'):
        logger.info("Trainer already optimized, skipping...")
        return trainer

    # 包装原始的train_step方法，添加模型内存优化
    original_train_step = trainer.train_step

    def optimized_train_step(samples, raise_oom=False):
        # 执行原始训练步骤（已经包含全局内存管理）
        result = original_train_step(samples, raise_oom)

        # 后处理模型内存优化
        if hasattr(trainer, 'model'):
            optimize_model_memory(trainer.model)

        return result

    trainer.train_step = optimized_train_step
    trainer._memory_optimized = True

    logger.info("Trainer memory optimizations applied successfully!")
    return trainer


def monitor_memory_usage(step_interval=100):
    """内存使用监控装饰器，使用全局内存管理器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)

            # 使用全局内存管理器的统计信息
            if global_memory_manager.step_count % step_interval == 0:
                stats = global_memory_manager.get_memory_stats()
                if torch.cuda.is_available():
                    logger.info(f"Step {global_memory_manager.step_count}: GPU Memory - "
                              f"Allocated: {stats['cuda_allocated_mb']:.0f}MB, "
                              f"Reserved: {stats['cuda_reserved_mb']:.0f}MB")

            return result
        return wrapper
    return decorator


def get_global_memory_manager():
    """获取全局内存管理器"""
    return global_memory_manager


def log_memory_usage(step=None):
    """记录当前内存使用情况"""
    stats = global_memory_manager.get_memory_stats()
    step_info = f"Step {step}: " if step is not None else ""

    if torch.cuda.is_available():
        logger.info(f"{step_info}Memory - "
                   f"Allocated: {stats['cuda_allocated_mb']:.0f}MB, "
                   f"Reserved: {stats['cuda_reserved_mb']:.0f}MB, "
                   f"Peak: {stats['peak_memory_mb']:.0f}MB")
    else:
        logger.info(f"{step_info}Memory - Peak: {stats['peak_memory_mb']:.0f}MB")


if __name__ == "__main__":
    # 可以直接运行此脚本来应用补丁
    apply_memory_patches()
    print("Memory optimization patches applied!")
