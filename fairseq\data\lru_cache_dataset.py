# Copyright (c) Facebook, Inc. and its affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

from functools import lru_cache

from . import BaseWrapperDataset


class LRUCacheDataset(BaseWrapperDataset):
    def __init__(self, dataset, token=None):
        super().__init__(dataset)

    @lru_cache(maxsize=4)  # 减少缓存大小以节省内存
    def __getitem__(self, index):
        return self.dataset[index]

    @lru_cache(maxsize=4)  # 减少缓存大小以节省内存
    def collater(self, samples):
        return self.dataset.collater(samples)

    def clear_cache(self):
        """清理LRU缓存"""
        self.__getitem__.cache_clear()
        self.collater.cache_clear()
