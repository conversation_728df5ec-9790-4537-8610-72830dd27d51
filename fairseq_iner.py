import torch
import torchaudio
# import sacrebleu
from argparse import Namespace
from fairseq import checkpoint_utils, options, scoring, tasks, utils
from fairseq.dataclass.utils import convert_namespace_to_omegaconf
from fairseq.data.audio.speech_to_speech_dataset import SpeechToSpeechDatasetItem, SpeechToTextDataset

parser = options.get_generation_parser()
args = options.parse_args_and_arch(parser, [
    '--task', 'speech_to_text', 
    '--path', r'C:\Users\<USER>\Downloads\export\AD\checkpoint_best.pt'
    '--config-yaml', r'C:\Users\<USER>\Downloads\export\AD\config_st.yaml',
    '--max-tokens', '1000',
    '--beam', '5',
    '--scoring', 'sacrebleu',
    '--prefix-size', '1'
    ])
if isinstance(args, Namespace):
    cfg = convert_namespace_to_omegaconf(args)

use_cuda = torch.cuda.is_available()
task = tasks.setup_task(cfg.task)
src_dict = getattr(task, 'source_dictionary', None)
tgt_dict = task.target_dictionary
models, saved_cfg = checkpoint_utils.load_model_ensemble(
    utils.split_paths(cfg.common_eval.path),
    task=task,
    suffix=cfg.checkpoint.checkpoint_suffix,
    strict=(cfg.checkpoint.checkpoint_shard_count == 1),
    num_shards=cfg.checkpoint.checkpoint_shard_count,
)
model = models[0]
if use_cuda:
    model = model.cuda()
model.prepare_for_inference_(cfg)
generator = task.build_generator(models, cfg.generation)
tokenizer = task.build_tokenizer(cfg.tokenizer)
bpe = task.build_bpe(cfg.bpe)
scorer = scoring.build_scorer(cfg.scoring, tgt_dict)


def load_sample(audio_path, src_lang='bo', tgt_lang='zh', reference=None):
    waveform, sr = torchaudio.load(audio_path)
    n_frames = int(waveform.size(1) / sr * 1000)

    ds = SpeechToTextDataset(
        split='test',
        is_train_split=False,
        cfg=cfg.task.data_cfg,
        audio_paths=[audio_path],
        n_frames=[n_frames],
        src_texts=[""],
        tgt_texts=[reference if reference else ""],
        speakers=['unknown'],
        src_langs=[src_lang],
        tgt_langs=[tgt_lang],
        ids=['test_sample'],
        tgt_dict=tgt_dict,
        pre_tokenizer=None,
        bpe_tokenizer=bpe,
        n_frames_per_step=1,
        speaker_to_id=None,
        append_eos=True
    )

    return ds.__getitem__(index=0)


single_sample = load_sample(audio_path='test.wav', reference='test reference')
print(single_sample)

prefix_tokens = tgt_dict.encode_line('zh', add_if_not_exist=True)[:, :cfg.generation.prefix_size]
hypos = task.inference_step(generator, models, single_sample, prefix_tokens=None)
hypo = hypos[0][0]
print(hypo)
