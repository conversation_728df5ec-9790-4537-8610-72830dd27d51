import torch
# import torchaudio
import math
# import sacrebleu
from argparse import Namespace
from fairseq import checkpoint_utils, options, scoring, tasks, utils
from fairseq.dataclass.utils import convert_namespace_to_omegaconf
from fairseq.data.audio.speech_to_speech_dataset import SpeechToTextDataset


class SpeechTranslator:
    """语音翻译模型类，支持加载不同语种的模型"""

    def __init__(self):
        self.task = None
        self.generator = None
        self.model = None
        self.bpe = None
        self.tgt_dict = None
        self.cfg = None
        self.use_cuda = torch.cuda.is_available()
        self.current_language = None

        # 定义不同语种的模型配置
        self.language_configs = {
            'ad': {
                'model_dir': r'C:\Users\<USER>\Downloads\export\AD',
                'checkpoint_path': r'C:\Users\<USER>\Downloads\export\AD\checkpoint_best.pt',
                'config_yaml': 'config_st.yaml',
                'src_lang': 'bo',
                'tgt_lang': 'zh'
            },
            'kb': {
                'model_dir': '/ws/exp/st/KB/',
                'checkpoint_path': '/ws/exp/st/KB/hot/checkpoint_best.pt',
                'config_yaml': 'config_st.yaml',
                'src_lang': 'bo',
                'tgt_lang': 'zh'
            },
            'wz': {
                'model_dir': '/ws/exp/st/WZ/',
                'checkpoint_path': '/ws/exp/st/WZ/hot/checkpoint_best.pt',
                'config_yaml': 'config_st.yaml',
                'src_lang': 'bo',
                'tgt_lang': 'zh'
            },
            'multi': {
                'model_dir': '/ws/exp/st/MULTI/',
                'checkpoint_path': '/ws/exp/st/MULTI/hot/checkpoint_best.pt',
                'config_yaml': 'config_st.yaml',
                'src_lang': 'bo',
                'tgt_lang': 'zh'
            }
        }

    def load_model(self, language='ad'):
        """加载指定语种的模型

        Args:
            language (str): 语种代码，支持 'ad', 'kb', 'wz', 'multi'
        """
        if language not in self.language_configs:
            raise ValueError(f"不支持的语种: {language}. 支持的语种: {list(self.language_configs.keys())}")

        config = self.language_configs[language]

        parser = options.get_generation_parser()
        args = options.parse_args_and_arch(parser, [
            config['model_dir'],
            '--task', 'speech_to_text',
            '--path', config['checkpoint_path'],
            '--config-yaml', config['config_yaml'],
            '--max-tokens', '1000',
            '--beam', '5',
            '--scoring', 'sacrebleu',
            '--prefix-size', '1'
        ])

        if isinstance(args, Namespace):
            self.cfg = convert_namespace_to_omegaconf(args)

        self.task = tasks.setup_task(self.cfg.task)
        # src_dict = getattr(self.task, 'source_dictionary', None)  # 未使用，注释掉
        self.tgt_dict = self.task.target_dictionary

        models, _saved_cfg = checkpoint_utils.load_model_ensemble(
            utils.split_paths(self.cfg.common_eval.path),
            task=self.task,
            suffix=self.cfg.checkpoint.checkpoint_suffix,
            strict=(self.cfg.checkpoint.checkpoint_shard_count == 1),
            num_shards=self.cfg.checkpoint.checkpoint_shard_count,
        )

        self.model = models[0]
        if self.use_cuda:
            self.model = self.model.cuda()

        self.model.prepare_for_inference_(self.cfg)
        self.generator = self.task.build_generator(models, self.cfg.generation)
        self.bpe = self.task.build_bpe(self.cfg.bpe)
        _scorer = scoring.build_scorer(self.cfg.scoring, self.tgt_dict)  # 未使用，但保留以防需要

        self.current_language = language
        print(f"已加载 {language} 语种模型")

    def load_sample(self, audio_path, src_lang='bo', tgt_lang='zh', reference=None):
        """加载音频样本数据

        Args:
            audio_path (str): 音频文件路径
            src_lang (str): 源语言代码
            tgt_lang (str): 目标语言代码
            reference (str): 参考翻译文本
        """
        if self.task is None:
            raise RuntimeError("模型未加载，请先调用 load_model() 方法")

        # waveform, sr = torchaudio.load(audio_path)
        # n_frames = int(waveform.size(1) / sr * 100)  # 具体数值不影响解码, 可省略
        ds = SpeechToTextDataset(
            split='test',
            is_train_split=False,
            cfg=self.task.data_cfg,
            audio_paths=[audio_path],
            n_frames=[100],
            src_texts=[""],
            tgt_texts=[reference if reference else ""],
            speakers=['unknown'],
            src_langs=[src_lang],
            tgt_langs=[tgt_lang],
            ids=['test_sample'],
            tgt_dict=self.tgt_dict,
            pre_tokenizer=None,
            bpe_tokenizer=self.bpe,
            n_frames_per_step=1,
            speaker_to_id=None,
            append_eos=True
        )

        return ds.collater([ds.__getitem__(index=0)])

    def decode_fn(self, x):
        """解码函数"""
        if self.bpe is not None:
            x = self.bpe.decode(x)
        # if tokenizer is not None:
        #     x = tokenizer.decode(x)
        return x

    def get_symbols_to_strip_from_output(self):
        """获取需要从输出中移除的符号"""
        if hasattr(self.generator, "symbols_to_strip_from_output"):
            return self.generator.symbols_to_strip_from_output
        else:
            return {self.generator.eos}

    def parse_result(self, hypos):
        """解析翻译结果

        Args:
            hypos: 模型输出的假设结果

        Returns:
            str: 格式化的翻译结果
        """
        # Process top predictions  (仅1个样本)
        sample_id = 0
        for _j, hypo in enumerate(hypos[0][: self.cfg.generation.nbest]):
            _hypo_tokens, hypo_str, _alignment = utils.post_process_prediction(
                hypo_tokens=hypo["tokens"].int().cpu(),
                src_str="",
                alignment=hypo["alignment"],
                align_dict=None,
                tgt_dict=self.tgt_dict,
                remove_bpe=self.cfg.common_eval.post_process,
                extra_symbols_to_ignore=self.get_symbols_to_strip_from_output(),
            )
            detok_hypo_str = self.decode_fn(hypo_str)
            if not self.cfg.common_eval.quiet:
                score = hypo["score"] / math.log(2)  # convert to base 2
                # original hypothesis (after tokenization and BPE)
                line1 = "H-{}\t{}\t{}".format(sample_id, score, hypo_str)
                # detokenized hypothesis
                line2 = "D-{}\t{}\t{}".format(sample_id, score, detok_hypo_str)
                line3 = "P-{}\t{}".format(
                        sample_id,
                        " ".join(
                            map(
                                lambda x: "{:.4f}".format(x),
                                # convert from base e to base 2
                                hypo["positional_scores"]
                                .div_(math.log(2))
                                .tolist(),
                            )
                        ),
                    )
        return '\n'.join([line1, line2, line3])

    def infer(self, audio_path, reference=None):
        """执行语音翻译推理

        Args:
            audio_path (str): 音频文件路径
            reference (str): 参考翻译文本（可选）

        Returns:
            str: 翻译结果
        """
        if self.task is None or self.current_language is None:
            raise RuntimeError("模型未加载，请先调用 load_model() 方法")

        # 获取当前语种的语言配置
        config = self.language_configs[self.current_language]

        model_input = self.load_sample(
            audio_path=audio_path,
            src_lang=config['src_lang'],
            tgt_lang=config['tgt_lang'],
            reference=reference
        )

        if self.use_cuda:
            model_input = utils.move_to_cuda(model_input)

        hypos = self.task.inference_step(self.generator, [self.model], model_input, prefix_tokens=None)
        return self.parse_result(hypos)

    def get_translation_only(self, audio_path, reference=None):
        """获取纯翻译文本（不包含评分等信息）

        Args:
            audio_path (str): 音频文件路径
            reference (str): 参考翻译文本（可选）

        Returns:
            str: 纯翻译文本
        """
        full_result = self.infer(audio_path, reference)
        lines = full_result.split('\n')
        # 提取 D- 开头的行，这是去标记化的翻译结果
        for line in lines:
            if line.startswith('D-'):
                parts = line.split('\t')
                if len(parts) >= 3:
                    return parts[2]  # 返回翻译文本部分
        return ""


# 为了向后兼容，保留全局函数接口
def load_model():
    """向后兼容的全局函数"""
    global _global_translator
    _global_translator = SpeechTranslator()
    _global_translator.load_model('ad')  # 默认加载 ad 语种

def infer(audio_path='/ws/export/wavs/(adzw)1405828.wav'):
    """向后兼容的全局函数"""
    global _global_translator
    if _global_translator is None:
        load_model()
    return _global_translator.infer(audio_path)

# 全局翻译器实例
_global_translator = None

if __name__ == '__main__':
    # 测试代码
    translator = SpeechTranslator()
    translator.load_model('ad')
    print(translator.infer('/ws/export/wavs/(adzw)1405828.wav'))