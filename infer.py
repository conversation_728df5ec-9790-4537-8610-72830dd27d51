import torch
# import torchaudio
import math
# import sacrebleu
from argparse import Namespace
from fairseq import checkpoint_utils, options, scoring, tasks, utils
from fairseq.dataclass.utils import convert_namespace_to_omegaconf
from fairseq.data.audio.speech_to_speech_dataset import SpeechToSpeechDatasetItem, SpeechToTextDataset

task = None
generator=None
model = None
bpe=None
tgt_dict = None
cfg = None
use_cuda = False


def load_model():
    global task, generator, model, bpe, tgt_dict, use_cuda, cfg
    parser = options.get_generation_parser()
    args = options.parse_args_and_arch(parser, [
        '/ws/exp/st/AD/',
        '--task', 'speech_to_text', 
        '--path', '/ws/exp/st/AD/hot/checkpoint_best.pt',
        '--config-yaml', 'config_st.yaml',
        '--max-tokens', '1000',
        '--beam', '5',
        '--scoring', 'sacrebleu',
        '--prefix-size', '1'
        ])
    if isinstance(args, Namespace):
        cfg = convert_namespace_to_omegaconf(args)

    task = tasks.setup_task(cfg.task)
    src_dict = getattr(task, 'source_dictionary', None)
    tgt_dict = task.target_dictionary
    models, saved_cfg = checkpoint_utils.load_model_ensemble(
        utils.split_paths(cfg.common_eval.path),
        task=task,
        suffix=cfg.checkpoint.checkpoint_suffix,
        strict=(cfg.checkpoint.checkpoint_shard_count == 1),
        num_shards=cfg.checkpoint.checkpoint_shard_count,
    )
    model = models[0]
    use_cuda = torch.cuda.is_available()
    if use_cuda:
        model = model.cuda()
    model.prepare_for_inference_(cfg)
    generator = task.build_generator(models, cfg.generation)
    # tokenizer = task.build_tokenizer(cfg.tokenizer)   # None
    bpe = task.build_bpe(cfg.bpe)
    scorer = scoring.build_scorer(cfg.scoring, tgt_dict)


def load_sample(audio_path, data_cfg, tgt_dict, bpe, src_lang='bo', tgt_lang='zh', reference=None):
    # waveform, sr = torchaudio.load(audio_path)
    # n_frames = int(waveform.size(1) / sr * 100)  # 具体数值不影响解码, 可省略
    ds = SpeechToTextDataset(
        split='test',
        is_train_split=False,
        cfg=task.data_cfg,
        audio_paths=[audio_path],
        n_frames=[100],
        src_texts=[""],
        tgt_texts=[reference if reference else ""],
        speakers=['unknown'],
        src_langs=[src_lang],
        tgt_langs=[tgt_lang],
        ids=['test_sample'],
        tgt_dict=tgt_dict,
        pre_tokenizer=None,
        bpe_tokenizer=bpe,
        n_frames_per_step=1,
        speaker_to_id=None,
        append_eos=True
    )

    return ds.collater([ds.__getitem__(index=0)])

def decode_fn(x):
    if bpe is not None:
        x = bpe.decode(x)
    # if tokenizer is not None:
    #     x = tokenizer.decode(x)
    return x

def get_symbols_to_strip_from_output(generator):
    if hasattr(generator, "symbols_to_strip_from_output"):
        return generator.symbols_to_strip_from_output
    else:
        return {generator.eos}

def parse_result(hypos):
    # Process top predictions  (仅1个样本)
    sample_id = 0
    for j, hypo in enumerate(hypos[0][: cfg.generation.nbest]):
        hypo_tokens, hypo_str, alignment = utils.post_process_prediction(
            hypo_tokens=hypo["tokens"].int().cpu(),
            src_str="",
            alignment=hypo["alignment"],
            align_dict=None,
            tgt_dict=tgt_dict,
            remove_bpe=cfg.common_eval.post_process,
            extra_symbols_to_ignore=get_symbols_to_strip_from_output(generator),
        )
        detok_hypo_str = decode_fn(hypo_str)
        if not cfg.common_eval.quiet:
            score = hypo["score"] / math.log(2)  # convert to base 2
            # original hypothesis (after tokenization and BPE)
            line1 = "H-{}\t{}\t{}".format(sample_id, score, hypo_str)
            # detokenized hypothesis
            line2 = "D-{}\t{}\t{}".format(sample_id, score, detok_hypo_str)
            line3 = "P-{}\t{}".format(
                    sample_id,
                    " ".join(
                        map(
                            lambda x: "{:.4f}".format(x),
                            # convert from base e to base 2
                            hypo["positional_scores"]
                            .div_(math.log(2))
                            .tolist(),
                        )
                    ),
                )
    return '\n'.join([line1, line2, line3])

def infer(audio_path='/ws/export/wavs/(adzw)1405828.wav'):
    model_input = load_sample(audio_path=audio_path, data_cfg=task.data_cfg, tgt_dict=tgt_dict, bpe=bpe, reference='test reference')
    if use_cuda:
        model_input = utils.move_to_cuda(model_input)
    hypos = task.inference_step(generator, [model], model_input, prefix_tokens=None)
    return parse_result(hypos)

if __name__ == '__main__':
    load_model()
    print(infer())