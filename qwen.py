import dashscope

messages = [
    {
        "role": "user",
        "content": [
            {"audio": r"C:\Users\<USER>\Desktop\877259.wav"},
            {"text": "这段藏语音频在说什么?"}
        ]
    }
]

response = dashscope.MultiModalConversation.call(
    model="qwen-audio-turbo-latest", 
    messages=messages,
    result_format="message"
    )
print("输出结果为：")
print(response["output"]["choices"][0]["message"].content[0]["text"])