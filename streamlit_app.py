from infer import infer, load_model
import streamlit as st
import tempfile
import os

load_model()

st.set_page_config(page_title="speech to text demo", layout='centered')
st.title("speech to text demo")

# 上传 WAV 文件（限制文件类型）
uploaded_file = st.file_uploader("上传 WAV 文件", type=["wav"])

filepath = st.text_input("输入 WAV 文件路径", value="/ws/data/ADZW_003.rar/28BB2C49494A403F7849EC7A44F71136/(adzw)039659.wav")

if uploaded_file is not None:
    # 获取文件字节数据
    bytes_data = uploaded_file.getvalue()

    # 创建临时文件并保存 WAV 数据
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmpfile:
        tmpfile.write(bytes_data)
        tmpfile_path = tmpfile.name  # 临时文件路径
        print(f"保存音频至: {tmpfile_path}")

    # 播放上传的音频
    st.audio(bytes_data, format="audio/wav")

    # 显示保存路径（实际应用中可替换为永久存储逻辑）
    st.success(f"文件已保存至临时路径: {tmpfile_path}")
    
    translation = infer(tmpfile_path)

    st.text_area(
        label="翻译结果",
        value=translation,
        height=200
    )

if filepath:

    ref_text = ""
    ref = filepath.replace('.wav', '.txt')
    if os.path.exists(ref):
        with open(ref, 'r', encoding='utf-8')as fr:
            ref_text = fr.read().strip()
    
    translation = infer(filepath)
    if len(ref_text):
        translation = f"参考答案: {ref_text}\n\n" + translation

    st.text_area(
        label="翻译结果",
        value=translation,
        height=200
    )