from infer import SpeechTranslator
import streamlit as st
import tempfile
import os

# 初始化会话状态
if 'translator' not in st.session_state:
    st.session_state.translator = SpeechTranslator()
if 'current_language' not in st.session_state:
    st.session_state.current_language = None
if 'translation_result' not in st.session_state:
    st.session_state.translation_result = ""

st.set_page_config(page_title="多语种语音翻译系统", layout='centered')
st.title("🎤 多语种语音翻译系统")

# 创建两列布局
col1, col2 = st.columns([2, 1])

with col1:
    st.subheader("📁 音频输入")

    # 文件上传选项
    input_method = st.radio(
        "选择输入方式:",
        ["上传文件", "输入文件路径"],
        horizontal=True
    )

    audio_path = None

    if input_method == "上传文件":
        uploaded_file = st.file_uploader(
            "上传 WAV 音频文件",
            type=["wav"],
            help="支持 WAV 格式的音频文件"
        )

        if uploaded_file is not None:
            # 获取文件字节数据
            bytes_data = uploaded_file.getvalue()

            # 创建临时文件并保存 WAV 数据
            with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmpfile:
                tmpfile.write(bytes_data)
                audio_path = tmpfile.name

            # 播放上传的音频
            st.audio(bytes_data, format="audio/wav")
            st.success(f"✅ 文件上传成功")

    else:  # 输入文件路径
        audio_path = st.text_input(
            "输入 WAV 文件路径:",
            value="/ws/data/ADZW_003.rar/28BB2C49494A403F7849EC7A44F71136/(adzw)039659.wav",
            help="请输入完整的音频文件路径"
        )

        if audio_path and os.path.exists(audio_path):
            st.success("✅ 文件路径有效")
        elif audio_path:
            st.error("❌ 文件路径不存在")

with col2:
    st.subheader("🌐 语种选择")

    # 语种选择下拉菜单
    language_options = {
        'ad': '安多藏语 (AD)',
        'kb': '康巴藏语 (KB)',
        'wz': '卫藏藏语 (WZ)',
        'multi': '多语种模型 (MULTI)'
    }

    selected_language = st.selectbox(
        "选择翻译语种:",
        options=list(language_options.keys()),
        format_func=lambda x: language_options[x],
        index=0
    )

    # 显示当前加载的模型状态
    if st.session_state.current_language:
        if st.session_state.current_language == selected_language:
            st.success(f"✅ 已加载: {language_options[selected_language]}")
        else:
            st.warning(f"⚠️ 当前模型: {language_options[st.session_state.current_language]}")
    else:
        st.info("ℹ️ 尚未加载模型")

# 提交按钮
st.markdown("---")
submit_button = st.button(
    "🚀 开始翻译",
    type="primary",
    disabled=not audio_path,
    use_container_width=True
)

# 处理翻译请求
if submit_button and audio_path:
    with st.spinner(f"正在加载 {language_options[selected_language]} 模型..."):
        try:
            # 如果需要切换模型，重新加载
            if st.session_state.current_language != selected_language:
                st.session_state.translator.load_model(selected_language)
                st.session_state.current_language = selected_language

            # 检查是否有参考文本
            ref_text = ""
            if input_method == "输入文件路径":
                ref_file = audio_path.replace('.wav', '.txt')
                if os.path.exists(ref_file):
                    with open(ref_file, 'r', encoding='utf-8') as fr:
                        ref_text = fr.read().strip()

            # 执行翻译
            with st.spinner("正在进行语音翻译..."):
                translation_result = st.session_state.translator.infer(audio_path, reference=ref_text)

                # 获取纯翻译文本
                clean_translation = st.session_state.translator.get_translation_only(audio_path, reference=ref_text)

                # 格式化结果
                if ref_text:
                    st.session_state.translation_result = f"参考答案: {ref_text}\n\n{translation_result}"
                else:
                    st.session_state.translation_result = translation_result

                st.success("✅ 翻译完成!")

        except Exception as e:
            st.error(f"❌ 翻译过程中出现错误: {str(e)}")

# 显示翻译结果
if st.session_state.translation_result:
    st.markdown("---")
    st.subheader("📝 翻译结果")

    # 显示详细结果
    st.text_area(
        label="详细翻译信息",
        value=st.session_state.translation_result,
        height=200,
        help="包含评分和详细信息的完整翻译结果"
    )

    # 如果有纯翻译文本，单独显示
    if st.session_state.current_language and audio_path:
        try:
            clean_text = st.session_state.translator.get_translation_only(audio_path)
            if clean_text:
                st.text_input(
                    "纯翻译文本:",
                    value=clean_text,
                    help="仅包含翻译文本，便于复制使用"
                )
        except:
            pass  # 如果获取失败，不显示

# 页面底部信息
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center; color: #666;'>
        <small>多语种语音翻译系统 | 支持安多藏语、康巴藏语、卫藏藏语及多语种模型</small>
    </div>
    """,
    unsafe_allow_html=True
)