from infer import SpeechTranslator
import streamlit as st
import pandas as pd
import tempfile
import os

st.set_page_config(page_title="Bo-Zh S2TT Demo", layout='centered')
st.title("🎤 Bo-Zh S2TT Demo")

# 初始化会话状态和模型预加载
@st.cache_resource
def initialize_translators():
    """初始化并预加载所有语种的翻译模型"""
    translators = {}
    language_configs = ['ad', 'kb', 'wz', 'multi']

    for lang in language_configs:
        try:
            translator = SpeechTranslator()
            translator.load_model(lang)
            translators[lang] = translator
            st.success(f"✅ {lang.upper()} 模型加载成功")
        except Exception as e:
            st.warning(f"⚠ {lang.upper()} 模型加载失败: {str(e)}")
            translators[lang] = None

    return translators

# 初始化会话状态
if 'translators' not in st.session_state:
    st.session_state.translators = initialize_translators()
if 'translation_result' not in st.session_state:
    st.session_state.translation_result = ""
if 'reference_text' not in st.session_state:
    st.session_state.reference_text = ""

# 创建左右两列布局
st.markdown("---")
st.write("从本地上传：")
col1, col2 = st.columns(2)

with col1:
    # 第1行：文件上传
    uploaded_file = st.file_uploader(
        "📤 上传音频文件",
        type=["wav"],
        help="支持 WAV 格式的音频文件"
    )
    reference = None

with col2:
    # 第2行：麦克风录制
    audio_bytes = st.audio_input("🎙️麦克风录制")
    reference = None

st.write("示例：")
df = pd.DataFrame({
    "language": ["WZ", "KB", "AD"],
    "path": ["/ws/wavs/(ADZW)B0003453.wav", "/ws/wavs/(ADZW)B0003453.wav", "/ws/wavs/(ADZW)B0003453.wav"],
    "text": ["他用什么语言通过迟到为自己辩护", "他用什么语言通过迟到为自己辩护", "他用什么语言通过迟到为自己辩护"],
})
edited_df = st.data_editor(
    df,
    column_config={
        "选择": st.column_config.CheckboxColumn("选择", default=False, help="选择要使用的示例")
    },
    num_rows="fixed",
    disabled=["language", "path", "text"],
    use_container_width=True,
    key="file_selector"
)

selected_rows = st.session_state.get("file_selector", {}).get("选择", [])
selected_indices = [i for i, row in enumerate(selected_rows) if row]
if selected_indices:
    selected_row = df.iloc[selected_indices[0]]
    st.session_state.selected_lang = selected_row["language"].lower()
    st.session_state.selected_file = selected_row["path"]
    reference = selected_row["text"]
else:
    st.session_state.selected_lang = None
    st.session_state.selected_file = None
    reference = None


# 第4行：音频播放（全宽度）
audio_path = None
audio_data = None
input_source = None

if uploaded_file is not None:
    # 处理上传的文件
    audio_data = uploaded_file.getvalue()
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmpfile:
        tmpfile.write(audio_data)
        audio_path = tmpfile.name
    input_source = "上传文件"
    st.audio(audio_data, format="audio/wav")
    st.success("✅ 文件上传成功")

elif audio_bytes is not None:
    # 处理麦克风录制
    audio_data = audio_bytes
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmpfile:
        tmpfile.write(audio_data)
        audio_path = tmpfile.name
    input_source = "麦克风录制"
    st.audio(audio_data, format="audio/wav")
    st.success("✅ 录制完成")

elif st.session_state.selected_file and os.path.exists(st.session_state.selected_file):
    # 处理文件路径
    audio_path = st.session_state.selected_file
    input_source = "文件路径"
    st.audio(st.session_state.selected_file)
    # st.success("✅ 有效路径")


# 提交按钮
st.markdown("---")
col3, col4 = st.columns(2)
with col3:
    # 语种选择下拉菜单
    language_options = {
        'ad': '安多藏语 (AD)',
        'kb': '康巴藏语 (KB)',
        'wz': '卫藏藏语 (WZ)',
        'multi': '多语种模型 (MULTI)'
    }

    if st.session_state.selected_lang:
        selected_language = st.session_state.selected_lang
    else:
        selected_language = st.selectbox(
            "选择语种:",
            options=list(language_options.keys()),
            format_func=lambda x: language_options[x],
            index=0
        )

    # 显示模型加载状态
    if st.session_state.translators.get(selected_language):
        st.success(f"✅ {language_options[selected_language]} 已就绪")
    else:
        st.error(f"❌ {language_options[selected_language]} 加载失败")

with col4:
    submit_button = st.button(
        "🚀 开始翻译",
        type="primary",
        disabled=not audio_path,
        use_container_width=True
    )

# 处理翻译请求
if submit_button and audio_path:
    # 检查选择的模型是否可用
    if not st.session_state.translators.get(selected_language):
        st.error(f"❌ {language_options[selected_language]} 模型不可用，请检查模型文件")
    else:
        try:
            # 使用预加载的模型
            translator = st.session_state.translators[selected_language]

            # 执行翻译
            with st.spinner(f"正在使用 {language_options[selected_language]} 进行语音翻译..."):
                translation_result = translator.infer(audio_path, reference="")

                # 获取纯翻译文本
                clean_translation = translator.get_translation_only(audio_path, reference=reference)

                # 格式化结果
                if reference:
                    st.session_state.translation_result = f"参考答案: {reference}\n\n{translation_result}"
                else:
                    st.session_state.translation_result = translation_result

                # 保存纯翻译文本到会话状态
                st.session_state.clean_translation = clean_translation

                st.success("✅ 翻译完成!")

        except Exception as e:
            st.error(f"❌ 翻译过程中出现错误: {str(e)}")

# 显示翻译结果
if st.session_state.translation_result:
    st.markdown("---")
    # st.subheader("📝 翻译结果")

    # 显示纯翻译文本（如果有的话）
    if hasattr(st.session_state, 'clean_translation') and st.session_state.clean_translation:
        st.text_input(
            "模型译文:",
            value=st.session_state.clean_translation,
            help="仅包含翻译文本，便于复制使用"
        )

    # 显示详细翻译信息
    st.text_area(
        label="详细翻译信息",
        value=st.session_state.translation_result,
        height=200,
        help="包含评分和详细信息的完整翻译结果"
    )

# 页面底部信息
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center; color: #666;'>
        <small> 语音翻译演示 | 支持安多藏语、康巴藏语、卫藏藏语及多语种模型 </small>
    </div>
    """,
    unsafe_allow_html=True
)